"""
Example usage of the reCAPTCHA solver.
"""
import os
import time
from recaptcha_solver import solver

# Configuration constants
DEFAULT_URL = "https://www.google.com/recaptcha/api2/demo"

DEFAULT_VERBOSE = True

def print_result(result, total_time):
    """Print the result of the reCAPTCHA solving attempt."""
    print("\n--- reCAPTCHA Solving Result ---")

    if result and result.get('success') and result.get('recaptcha_token'):
        print("✅ Success! reCAPTCHA appears to be solved.")
        token = result['recaptcha_token']
        print(f"Token: {token}")
        print(f"Time taken: {result.get('time_taken', 'N/A')} seconds")
    elif result and not result.get('recaptcha_token'):
        print("⚠️  Process completed, but reCAPTCHA token was NOT found.")
        print(f"Time taken: {result.get('time_taken', 'N/A')} seconds")
    else:
        print("❌ Failed to solve reCAPTCHA or an error occurred.")
        if result and 'error' in result:
            print(f"Error: {result['error']}")
        print(f"Total time: {total_time:.2f} seconds")

def main():
    """Main function to run the reCAPTCHA solver example."""
    # Configuration
    url = DEFAULT_URL
    verbose = DEFAULT_VERBOSE
    proxy = None

    print("🚀 Starting reCAPTCHA Solver Example...\n")

    # Set environment variable to ignore certificate errors
    os.environ['PYTHONHTTPSVERIFY'] = '0'

    print(f"🎯 Attempting to solve reCAPTCHA on: {url}")
    print("📝 Note: Certificate errors will be bypassed automatically.\n")

    start_time = time.time()

    try:
        result = solver(
            url=url,
            proxy=proxy,
            cookies=None,
            verbose=verbose
        )
    except Exception as e:
        result = {"success": False, "error": str(e), "time_taken": 0}

    end_time = time.time()
    total_time = end_time - start_time

    print_result(result, total_time)
    print("\n🏁 Example finished.")

if __name__ == "__main__":
    main()
